import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { formatDistanceToNow } from 'date-fns';
import { ChevronDown, Search } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { CvProfileFilter, cvProfileFilterData, Paging } from 'shared/types';
import { useDebouncedCallback } from 'use-debounce';

import { MembersList } from './MembersList';

import {
  ButtonSecondary,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Input,
  Loader,
  Pagination,
} from '@/components/common';
import { OrganizationData, useAuth } from '@/contexts/AuthContext';
import { SETTINGS_PATH } from '@/helpers/constants';
import {
  getMuchskillsMembers,
  syncMuchskillsMembers,
} from '@/helpers/requests';

interface MuchskillsViewProps {
  organization: OrganizationData;
}

export function MuchskillsView({ organization }: MuchskillsViewProps) {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { refreshUserData } = useAuth();

  //Search value to control input
  const [searchValue, setSearchValue] = useState<string>('');
  //Search value to pass to request
  const [searchValueReq, setSearchValueReq] = useState<string>('');
  const [paging, setPaging] = useState<Paging>({
    page: 1,
    itemsPerPage: 12,
  });
  const [filter, setFilter] = useState<CvProfileFilter>(CvProfileFilter.all);

  const {
    data: muchskillsMembersData,
    isFetching: muchskillsMembersDataFetching,
    refetch: refetchMuchskillsMembersData,
  } = useQuery({
    queryKey: [
      'muchskillsMembers',
      {
        orgId: organization._id,
        paging,
        searchValue: searchValueReq,
        filter,
      },
    ],
    queryFn: () =>
      getMuchskillsMembers({
        orgId: organization._id,
        paging,
        searchValue: searchValueReq,
        filter,
      }),
    placeholderData: keepPreviousData,
  });

  const { mutate: syncMembers, isPending: syncIsPending } = useMutation({
    mutationFn: () => syncMuchskillsMembers(organization._id),
    onSuccess: () => {
      resetPaging();
      refetchMuchskillsMembersData();
    },
    onSettled: () => {
      refreshUserData();
      queryClient.invalidateQueries({ queryKey: ['paginatedMembers'] });
    },
  });

  const changeSearchValueDebounced = useDebouncedCallback((val: string) => {
    setSearchValueReq(val);
    resetPaging();
  }, 500);

  const resetPaging = () => setPaging((prev) => ({ ...prev, page: 1 }));

  if (!organization.muchskillsIntegration?.connected) {
    return (
      <div className="flex flex-col items-center justify-center border border-dashed rounded-[8px] px-2 py-10">
        <img
          className="mb-4"
          width={352}
          src="/images/muchskills-cvinventory.png"
          alt="no matches"
        />
        <span className="text-smalldoge-3 mb-12 max-w-[320px] text-center">
          Sync <b>MuchSkills</b> profiles to <b>CVInventory</b> for easier
          profile management and data sync
        </span>
        <ButtonSecondary onClick={() => navigate(SETTINGS_PATH.integrations)}>
          Set up API token
        </ButtonSecondary>
      </div>
    );
  }

  return (
    <>
      <div className="flex items-center mb-4 px-2 py-1 rounded-sm bg-msGray-6">
        {syncIsPending ? (
          <Loader size={16} />
        ) : (
          organization.muchskillsIntegration.lastSync && (
            <span className="text-msGray-3 text-smalldoge-3">
              Last synced{' '}
              {formatDistanceToNow(
                organization.muchskillsIntegration?.lastSync,
              )}{' '}
              ago
            </span>
          )
        )}
        <ButtonSecondary
          className="ml-auto"
          onClick={() => syncMembers()}
          disabled={syncIsPending}
        >
          Sync now
        </ButtonSecondary>
      </div>
      <div className="p-px mb-6">
        <Input
          prefixElement={
            <Search
              className="absolute transform -translate-y-2.5 translate-x-2 top-[50%]"
              size={20}
            />
          }
          value={searchValue}
          placeholder="Search profile to create CV"
          className="h-12 pl-8"
          onChange={(e) => {
            setSearchValue(e.target.value);
            changeSearchValueDebounced(e.target.value);
          }}
        />
      </div>
      <div className="flex justify-between mb-2">
        <div className="flex space-x-1">
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center bg-msWhite border border-msGray-5 rounded-[100px] px-2 cursor-pointer">
                <span className="font-bold text-smalldoge-4">
                  Showing {cvProfileFilterData[filter].name}
                </span>
                <ChevronDown size={16} />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="start"
              className="prevent-drawer-outside-click"
            >
              <DropdownMenuGroup>
                {Object.values(CvProfileFilter).map((filter) => (
                  <DropdownMenuItem
                    key={filter}
                    onClick={() => {
                      setFilter(filter);
                      resetPaging();
                    }}
                  >
                    <span className="text-smalldoge-3">
                      {cvProfileFilterData[filter].name}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <Pagination
          page={paging.page}
          itemsPerPage={paging.itemsPerPage}
          total={muchskillsMembersData?.total}
          onItemsPerPageChange={(val) =>
            setPaging({ ...paging, itemsPerPage: val, page: 1 })
          }
          onPageChange={(page) => setPaging({ ...paging, page })}
        />
      </div>
      <MembersList
        orgId={organization._id}
        membersFetching={muchskillsMembersDataFetching}
        noMembersFound={
          !muchskillsMembersDataFetching && !muchskillsMembersData?.total
        }
        members={muchskillsMembersData?.members}
        onMemberAdded={() => {
          setSearchValue('');
          setSearchValueReq('');
          resetPaging();

          setTimeout(() => refetchMuchskillsMembersData(), 0);
        }}
      />
    </>
  );
}
