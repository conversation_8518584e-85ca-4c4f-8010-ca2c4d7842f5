import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import {
  CostRate,
  Currency,
  Member,
  MemberSource,
  TimeRange,
  OrganizationFullInfo,
} from 'shared/types';
import { toast } from 'sonner';

import {
  BasicDataForm,
  CertificationsForm,
  MemberEducationForm,
  MemberWorkHistoryForm,
  SkillsForm,
} from './components';
import { MemberProfileFormValues, memberProfileSchema } from './types';
import {
  createMemberRequest,
  updateMemberRequest,
} from '../../helpers/requests';

import {
  ButtonSecondary,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components';

const defaultCostRateData: CostRate = {
  currency: Currency.usd,
  amount: 0,
  timeRange: TimeRange.month,
};

export const initialFormState = {
  avatarFile: undefined,
  avatarPreview: undefined,
  firstName: '',
  lastName: '',
  email: '',
  location: '',
  telephone: '',
  currentPosition: '',
  currentLevel: '',
  type: undefined,
  clients: [],
  costRate: defaultCostRateData,
  costToCompany: defaultCostRateData,
  yearsOfExperience: 0,
  languages: [],
  socials: [],
  education: [],
  workExperience: [],
  skills: [],
  certifications: [],
};

interface MemberProfileFormProps {
  orgId?: string;
  member?: Member;
  onClose: () => void;
}

export function MemberProfileForm({
  orgId,
  member,
  onClose,
}: MemberProfileFormProps) {
  const queryClient = useQueryClient();

  const methods = useForm<MemberProfileFormValues>({
    resolver: zodResolver(memberProfileSchema),
    defaultValues: initialFormState,
  });
  const {
    handleSubmit,
    reset,
    formState: { errors },
  } = methods;

  const { mutate: createNewMember } = useMutation({
    mutationFn: (data: MemberProfileFormValues) =>
      createMemberRequest(
        {
          ...data,
          socials: data.socials.filter((soc) => soc.trim()),
          avatarFile: undefined,
          avatarPreview: undefined,
          source: MemberSource.cvinventory,
        },
        data.avatarFile,
      ),
    onError: (error) => {
      toast.error(error.message);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paginatedMembers'] });
      // Update organization full info to increment totalMembers count
      queryClient.setQueryData(
        ['organizationFullInfo'],
        (oldData: OrganizationFullInfo | undefined) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            totalMembers: oldData.totalMembers + 1,
          };
        },
      );

      reset(initialFormState);
      toast.success('Member created!');
      onClose();
    },
  });

  const { mutate: updateMember } = useMutation({
    mutationFn: ({
      memberId,
      data,
    }: {
      memberId: string;
      data: MemberProfileFormValues;
    }) =>
      updateMemberRequest(
        memberId,
        {
          ...data,
          socials: data.socials.filter((soc) => soc.trim()),
          avatarFile: undefined,
          avatarPreview: undefined,
        },
        data.avatarFile,
      ),
    onError: (error) => {
      toast.error(error.message);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['paginatedMembers'] });
      queryClient.invalidateQueries({
        queryKey: ['member', { memberId: variables.memberId }],
      });
      toast.success('Member updated!');
    },
  });

  const resetMemberData = useCallback(
    (member: Member) => {
      reset({
        avatarFile: undefined,
        avatarPreview: member.avatar,
        firstName: member.firstName,
        lastName: member.lastName || '',
        email: member.email || '',
        location: member.location || '',
        telephone: member.telephone || '',
        currentPosition: member.currentPosition || '',
        currentLevel: member.currentLevel,
        type: member.type,
        clients: member.clients,
        costRate: member.costRate || defaultCostRateData,
        costToCompany: member.costToCompany || defaultCostRateData,
        yearsOfExperience: member.yearsOfExperience || 0,
        languages: member.languages,
        socials: member.socials,
        education: member.education.sort((a, b) => {
          if (!a.startDate && !b.startDate) return 0;
          if (!a.startDate) return 1;
          if (!b.startDate) return -1;
          return (
            new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
          );
        }),
        workExperience: member.workExperience.sort(
          (a, b) =>
            new Date(b.startDate).getTime() - new Date(a.startDate).getTime(),
        ),
        certifications: member.certifications,
        skills: member.skills,
      });
    },
    [reset],
  );

  useEffect(() => {
    if (member) resetMemberData(member);
  }, [member, resetMemberData]);

  const handleCancel = () => {
    if (member) {
      onClose();
      resetMemberData(member);
    } else {
      reset(initialFormState);
    }
  };

  return (
    <div className="flex flex-col h-full min-h-0 rounded-lg border border-msGray-5 p-4">
      <FormProvider {...methods}>
        <form
          className="flex flex-col min-h-0"
          onSubmit={handleSubmit(
            (data) => {
              if (member) {
                updateMember({ memberId: member._id, data });
              } else {
                createNewMember(data);
              }
            },
            () => toast.error('Please fix invalid data before submitting.'),
          )}
        >
          <div className="flex space-x-10 mb-2">
            <b className="text-smalldoge-2 text-msGray-3 grow">
              Manage member details below
            </b>
            <div className="flex space-x-2 shrink-0">
              <ButtonSecondary
                variant="ghost"
                type="button"
                onClick={handleCancel}
              >
                Cancel
              </ButtonSecondary>
              <ButtonSecondary variant="white" type="submit">
                {member ? 'Save Changes' : 'Create Profile'}
              </ButtonSecondary>
            </div>
          </div>
          <Tabs className="flex flex-col min-h-0" defaultValue="basicProfile">
            <div className="mb-4 overflow-x-auto flex-shrink-0">
              <TabsList className="flex flex-row w-max">
                <TabsTrigger
                  value="basicProfile"
                  error={
                    !!errors.firstName || !!errors.email || !!errors.telephone
                  }
                >
                  <span className="uppercase">Profile</span>
                </TabsTrigger>
                <TabsTrigger value="education" error={!!errors.education}>
                  <span className="uppercase">Education</span>
                </TabsTrigger>
                <TabsTrigger
                  value="workExperience"
                  error={!!errors.workExperience}
                >
                  <span className="uppercase">Work Experience</span>
                </TabsTrigger>
                <TabsTrigger value="certifications">
                  <span className="uppercase">Certifications</span>
                </TabsTrigger>
                <TabsTrigger value="skills">
                  <span className="uppercase">Skills</span>
                </TabsTrigger>
              </TabsList>
            </div>
            <TabsContent
              className="flex flex-col min-h-0 overflow-auto"
              value="basicProfile"
            >
              <BasicDataForm memberSource={member?.source} />
            </TabsContent>
            <TabsContent
              className="flex flex-col min-h-0 overflow-auto"
              value="education"
            >
              <MemberEducationForm
                readonly={member?.source === MemberSource.muchskills}
                externalMember={member?.source === MemberSource.muchskills}
              />
            </TabsContent>
            <TabsContent
              className="flex flex-col min-h-0 overflow-auto"
              value="workExperience"
            >
              <MemberWorkHistoryForm
                readonly={member?.source === MemberSource.muchskills}
                externalMember={member?.source === MemberSource.muchskills}
              />
            </TabsContent>
            <TabsContent
              className="flex flex-col min-h-0 overflow-auto"
              value="certifications"
            >
              <CertificationsForm
                readonly={member?.source === MemberSource.muchskills}
                externalMember={member?.source === MemberSource.muchskills}
              />
            </TabsContent>
            <TabsContent className="flex flex-col min-h-0" value="skills">
              <SkillsForm
                orgId={orgId}
                readonly={member?.source === MemberSource.muchskills}
                externalMember={member?.source === MemberSource.muchskills}
              />
            </TabsContent>
          </Tabs>
        </form>
      </FormProvider>
    </div>
  );
}
